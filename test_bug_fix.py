#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试bug修复
验证customer_bypass_test_service接口是否正常工作
"""

import requests
import json

# 测试配置
BASE_URL = "http://38.150.2.100:8094"
API_KEY = "SB_DEFAULT_API_2025_F5SHAPE_BYPASS"

def test_bypass_service():
    """测试绕过服务接口"""
    print("🔧 测试bug修复 - 客户绕过测试服务")
    print("=" * 50)
    
    # 测试数据
    test_data = {
        "target_url": "https://httpbin.org/get",
        "test_config": {
            "device_count": 3,
            "method": "GET",
            "concurrent_limit": 2
        }
    }
    
    headers = {
        "X-API-Key": API_KEY,
        "Content-Type": "application/json"
    }
    
    print(f"📡 发送请求到: {BASE_URL}/api/bypass/test-service")
    print(f"🔑 使用API密钥: {API_KEY}")
    print(f"📋 测试数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    print()
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/bypass/test-service",
            headers=headers,
            json=test_data,
            timeout=60
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功!")
            print(f"📈 响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 检查关键字段
            if result.get("success"):
                print("✅ Bug修复成功 - 接口正常工作!")
                
                # 检查测试结果
                if "data" in result and "test_results" in result["data"]:
                    test_results = result["data"]["test_results"]
                    print(f"📊 测试结果数量: {len(test_results)}")
                    
                    for i, test_result in enumerate(test_results[:3]):  # 只显示前3个
                        print(f"  设备{i}: 成功={test_result.get('success', False)}, "
                              f"响应时间={test_result.get('response_time', 0):.3f}s")
                
                if "summary" in result["data"]:
                    summary = result["data"]["summary"]
                    print(f"📈 测试摘要:")
                    print(f"  成功率: {summary.get('success_rate', 0):.2%}")
                    print(f"  平均响应时间: {summary.get('avg_response_time', 0):.3f}s")
                    
            else:
                print("❌ 接口返回失败状态")
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 请确认服务器是否运行")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")

def test_health_check():
    """测试健康检查"""
    print("\n🏥 测试健康检查")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        print(f"📊 健康检查状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器状态异常")
            
    except Exception as e:
        print(f"❌ 健康检查失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始测试bug修复...")
    print()
    
    # 先测试健康检查
    test_health_check()
    
    # 再测试主要功能
    test_bypass_service()
    
    print("\n🎯 测试完成!")
