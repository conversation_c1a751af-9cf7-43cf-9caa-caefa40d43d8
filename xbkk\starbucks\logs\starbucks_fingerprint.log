2025-7-29 04:38:10 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 04:38:10 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:38:10 - src.core.f5_shape_generator - ERROR - 深度分析失败: F5ShapeAnalyzer._analyze_e_time_correlation() takes 1 positional argument but 2 were given
2025-7-29 04:38:10 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:38:10 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:39:06 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 04:39:06 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:39:06 - src.core.f5_shape_generator - ERROR - 深度分析失败: F5ShapeAnalyzer._analyze_e_time_correlation() takes 1 positional argument but 2 were given
2025-7-29 04:39:06 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:39:06 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:39:07 - __main__ - INFO - 启动风控绕过系统测试，设备数量: 3
2025-7-29 04:39:07 - __main__ - INFO - ============================================================
2025-7-29 04:39:07 - __main__ - INFO - 开始星巴克F5 Shape风控绕过系统综合测试
2025-7-29 04:39:07 - __main__ - INFO - ============================================================
2025-7-29 04:39:07 - __main__ - INFO - 测试时间: 2025-7-29 04:39:07
2025-7-29 04:39:07 - __main__ - INFO - 测试端点: https://httpbin.org/get
2025-7-29 04:39:07 - __main__ - INFO - 测试设备数量: 3
2025-7-29 04:39:07 - __main__ - INFO - 最大并发数: 30
2025-7-29 04:39:07 - __main__ - INFO - 
第一步：建立风控检测基准
2025-7-29 04:39:07 - src.utils.bypass_tester - INFO - 开始建立风控检测基准...
2025-7-29 04:39:13 - src.utils.bypass_tester - INFO - 基准测试 no_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:39:14 - src.utils.bypass_tester - INFO - 基准测试 invalid_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:39:15 - src.utils.bypass_tester - INFO - 基准测试 empty_headers: 状态码=200, 被拦截=False
2025-7-29 04:39:15 - src.utils.bypass_tester - INFO - 基准测试完成，0/3 个测试被风控拦截
2025-7-29 04:39:15 - __main__ - INFO - 风控检测基准测试结果:
2025-7-29 04:39:15 - __main__ - INFO -   no_fingerprint: 通过 (状态码: 200)
2025-7-29 04:39:15 - __main__ - INFO -   invalid_fingerprint: 通过 (状态码: 200)
2025-7-29 04:39:15 - __main__ - INFO -   empty_headers: 通过 (状态码: 200)
2025-7-29 04:39:15 - __main__ - INFO - 
第二步：初始化设备池
2025-7-29 04:39:15 - __main__ - ERROR - 测试过程中发生错误: 'DeviceManager' object has no attribute 'initialize_device_pool'
2025-7-29 04:40:24 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 04:40:24 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:40:24 - src.core.f5_shape_generator - ERROR - 深度分析失败: F5ShapeAnalyzer._analyze_e_time_correlation() takes 1 positional argument but 2 were given
2025-7-29 04:40:24 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:40:24 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:40:24 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:40:24 - src.core.f5_shape_generator - ERROR - 深度分析失败: F5ShapeAnalyzer._analyze_e_time_correlation() takes 1 positional argument but 2 were given
2025-7-29 04:40:24 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:40:24 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:42:25 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 04:42:25 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:42:25 - src.core.f5_shape_generator - ERROR - 深度分析失败: F5ShapeAnalyzer._analyze_e_time_correlation() takes 1 positional argument but 2 were given
2025-7-29 04:42:25 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:42:25 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:42:26 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:42:26 - src.core.f5_shape_generator - ERROR - 深度分析失败: F5ShapeAnalyzer._analyze_e_time_correlation() takes 1 positional argument but 2 were given
2025-7-29 04:42:26 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:42:26 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:42:26 - src.utils.bypass_tester - INFO - 开始建立风控检测基准...
2025-7-29 04:42:33 - src.utils.bypass_tester - INFO - 基准测试 no_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:42:34 - src.utils.bypass_tester - INFO - 基准测试 invalid_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:42:36 - src.utils.bypass_tester - INFO - 基准测试 empty_headers: 状态码=200, 被拦截=False
2025-7-29 04:42:36 - src.utils.bypass_tester - INFO - 基准测试完成，0/3 个测试被风控拦截
2025-7-29 04:42:58 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 04:42:58 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:42:58 - src.core.f5_shape_generator - ERROR - 深度分析失败: F5ShapeAnalyzer._analyze_e_time_correlation() takes 1 positional argument but 2 were given
2025-7-29 04:42:58 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:42:58 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:42:58 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:42:58 - src.core.f5_shape_generator - ERROR - 深度分析失败: F5ShapeAnalyzer._analyze_e_time_correlation() takes 1 positional argument but 2 were given
2025-7-29 04:42:58 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:42:58 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:42:58 - src.utils.bypass_tester - INFO - 开始建立风控检测基准...
2025-7-29 04:43:04 - src.utils.bypass_tester - INFO - 基准测试 no_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:43:04 - src.utils.bypass_tester - INFO - 基准测试 invalid_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:43:06 - src.utils.bypass_tester - INFO - 基准测试 empty_headers: 状态码=200, 被拦截=False
2025-7-29 04:43:06 - src.utils.bypass_tester - INFO - 基准测试完成，0/3 个测试被风控拦截
2025-7-29 04:44:04 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 04:44:04 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:44:04 - src.core.f5_shape_generator - ERROR - 深度分析失败: F5ShapeAnalyzer._analyze_e_time_correlation() takes 1 positional argument but 2 were given
2025-7-29 04:44:04 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:44:04 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:44:04 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:44:04 - src.core.f5_shape_generator - ERROR - 深度分析失败: F5ShapeAnalyzer._analyze_e_time_correlation() takes 1 positional argument but 2 were given
2025-7-29 04:44:04 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:44:04 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:44:04 - src.utils.bypass_tester - INFO - 开始建立风控检测基准...
2025-7-29 04:44:09 - src.utils.bypass_tester - INFO - 基准测试 no_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:44:10 - src.utils.bypass_tester - INFO - 基准测试 invalid_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:44:10 - src.utils.bypass_tester - INFO - 基准测试 empty_headers: 状态码=200, 被拦截=False
2025-7-29 04:44:10 - src.utils.bypass_tester - INFO - 基准测试完成，0/3 个测试被风控拦截
2025-7-29 04:48:07 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 04:48:07 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:48:07 - src.core.f5_shape_generator - ERROR - 深度分析失败: 'float' object has no attribute 'bit_length'
2025-7-29 04:48:07 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:48:07 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:48:07 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:48:07 - src.core.f5_shape_generator - ERROR - 深度分析失败: 'float' object has no attribute 'bit_length'
2025-7-29 04:48:07 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:48:07 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:48:07 - src.utils.bypass_tester - INFO - 开始建立风控检测基准...
2025-7-29 04:48:13 - src.utils.bypass_tester - INFO - 基准测试 no_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:48:15 - src.utils.bypass_tester - INFO - 基准测试 invalid_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:48:15 - src.utils.bypass_tester - INFO - 基准测试 empty_headers: 状态码=200, 被拦截=False
2025-7-29 04:48:15 - src.utils.bypass_tester - INFO - 基准测试完成，0/3 个测试被风控拦截
2025-7-29 04:49:09 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 04:49:09 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:49:09 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 04:49:09 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:49:09 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:49:10 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:49:10 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 04:49:10 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:49:10 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:49:10 - src.utils.bypass_tester - INFO - 开始建立风控检测基准...
2025-7-29 04:49:15 - src.utils.bypass_tester - INFO - 基准测试 no_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:49:15 - src.utils.bypass_tester - INFO - 基准测试 invalid_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:49:16 - src.utils.bypass_tester - INFO - 基准测试 empty_headers: 状态码=200, 被拦截=False
2025-7-29 04:49:16 - src.utils.bypass_tester - INFO - 基准测试完成，0/3 个测试被风控拦截
2025-7-29 04:52:44 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 04:52:44 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:52:44 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 04:52:44 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:52:44 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:52:44 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 04:52:44 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 04:52:44 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 04:52:44 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 04:52:44 - src.utils.bypass_tester - INFO - 开始建立风控检测基准...
2025-7-29 04:52:49 - src.utils.bypass_tester - INFO - 基准测试 no_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:52:51 - src.utils.bypass_tester - INFO - 基准测试 invalid_fingerprint: 状态码=200, 被拦截=False
2025-7-29 04:52:51 - src.utils.bypass_tester - INFO - 基准测试 empty_headers: 状态码=200, 被拦截=False
2025-7-29 04:52:51 - src.utils.bypass_tester - INFO - 基准测试完成，0/3 个测试被风控拦截
2025-7-29 05:00:36 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 05:00:36 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 05:00:36 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 05:00:36 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 05:00:36 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 05:00:36 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 05:00:36 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 05:00:36 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 05:00:36 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 05:00:36 - src.utils.bypass_tester - INFO - 开始建立风控检测基准...
2025-7-29 05:00:41 - src.utils.bypass_tester - INFO - 基准测试 no_fingerprint: 状态码=200, 被拦截=False
2025-7-29 05:00:41 - src.utils.bypass_tester - INFO - 基准测试 invalid_fingerprint: 状态码=200, 被拦截=False
2025-7-29 05:00:42 - src.utils.bypass_tester - INFO - 基准测试 empty_headers: 状态码=200, 被拦截=False
2025-7-29 05:00:42 - src.utils.bypass_tester - INFO - 基准测试完成，0/3 个测试被风控拦截
2025-7-29 05:02:43 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 05:02:43 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 05:02:43 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 05:02:43 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 05:02:43 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 05:02:43 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 05:02:43 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 05:02:43 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 05:02:43 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 05:02:43 - src.utils.bypass_tester - INFO - 开始建立风控检测基准...
2025-7-29 05:02:57 - src.utils.bypass_tester - INFO - 基准测试 no_fingerprint: 状态码=200, 被拦截=False
2025-7-29 05:02:57 - src.utils.bypass_tester - INFO - 基准测试 invalid_fingerprint: 状态码=200, 被拦截=False
2025-7-29 05:02:57 - src.utils.bypass_tester - INFO - 基准测试 empty_headers: 状态码=200, 被拦截=False
2025-7-29 05:02:57 - src.utils.bypass_tester - INFO - 基准测试完成，0/3 个测试被风控拦截
2025-7-29 05:06:04 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 05:06:04 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 05:06:04 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 05:06:04 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 05:06:04 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 05:06:04 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 05:06:04 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 05:06:04 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 05:06:04 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 05:06:04 - src.utils.bypass_tester - INFO - 开始建立风控检测基准...
2025-7-29 05:06:09 - src.utils.bypass_tester - INFO - 基准测试 no_fingerprint: 状态码=200, 被拦截=False
2025-7-29 05:06:11 - src.utils.bypass_tester - INFO - 基准测试 invalid_fingerprint: 状态码=200, 被拦截=False
2025-7-29 05:06:12 - src.utils.bypass_tester - INFO - 基准测试 empty_headers: 状态码=200, 被拦截=False
2025-7-29 05:06:12 - src.utils.bypass_tester - INFO - 基准测试完成，0/3 个测试被风控拦截
2025-7-29 05:06:32 - starbucks_fingerprint - INFO - 日志系统初始化完成
2025-7-29 05:06:32 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 05:06:32 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 05:06:32 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 05:06:32 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 05:06:32 - src.core.f5_shape_generator - INFO - 开始深度分析F5 Shape指纹数据...
2025-7-29 05:06:32 - src.core.f5_shape_generator - INFO - F5 Shape深度分析完成
2025-7-29 05:06:32 - src.core.f5_shape_generator - INFO - 算法参数初始化完成
2025-7-29 05:06:32 - src.core.f5_shape_generator - INFO - F5 Shape生成器初始化完成，已加载真实算法参数
2025-7-29 05:06:32 - src.utils.bypass_tester - INFO - 开始建立风控检测基准...
2025-7-29 05:06:38 - src.utils.bypass_tester - INFO - 基准测试 no_fingerprint: 状态码=200, 被拦截=False
2025-7-29 05:06:39 - src.utils.bypass_tester - INFO - 基准测试 invalid_fingerprint: 状态码=200, 被拦截=False
2025-7-29 05:06:39 - src.utils.bypass_tester - INFO - 基准测试 empty_headers: 状态码=200, 被拦截=False
2025-7-29 05:06:39 - src.utils.bypass_tester - INFO - 基准测试完成，0/3 个测试被风控拦截
