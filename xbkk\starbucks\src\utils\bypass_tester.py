#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风控绕过测试器 - 增强版
作者：YINGAshadow
创建时间：2025-7-29
修改时间：2025-7-29
功能：真实测试设备指纹绕过F5 Shape风控的效果
"""

import asyncio
import time
import json
import re
from typing import Dict, List, Optional, Tuple, Any
import httpx
from datetime import datetime

from ..config.settings import settings
from ..core.device_manager import Device, device_manager
from .logger import setup_logger, log_bypass_test, log_error_with_context


class BypassTester:
    """风控绕过测试器类 - 增强版"""

    def __init__(self):
        """初始化测试器"""
        self.logger = setup_logger(__name__)
        self.client = None

        # 风控检测基准
        self.baseline_established = False
        self.baseline_results = {}

        # 风控特征识别
        self.risk_control_patterns = {
            "blocked_status_codes": [403, 429, 451, 503],
            "blocked_keywords": settings.BLOCKED_INDICATORS,
            "suspicious_headers": ["x-rate-limit", "x-blocked", "x-security"],
            "challenge_patterns": ["captcha", "verification", "challenge"]
        }

    async def establish_baseline(self, test_endpoint: str) -> Dict:
        """建立风控检测基准"""
        self.logger.info("开始建立风控检测基准...")

        baseline_tests = [
            ("no_fingerprint", {}),  # 无指纹请求
            ("invalid_fingerprint", self._generate_invalid_fingerprint()),  # 错误指纹
            ("empty_headers", {"User-Agent": ""}),  # 空头部
        ]

        baseline_results = {}

        for test_name, headers in baseline_tests:
            try:
                result = await self._perform_baseline_test(test_endpoint, headers, test_name)
                baseline_results[test_name] = result

                self.logger.info(f"基准测试 {test_name}: 状态码={result['status_code']}, "
                               f"被拦截={result['is_blocked']}")

            except Exception as e:
                self.logger.error(f"基准测试 {test_name} 失败: {str(e)}")
                baseline_results[test_name] = {"error": str(e), "is_blocked": True}

        self.baseline_results = baseline_results
        self.baseline_established = True

        # 分析基准结果
        blocked_count = sum(1 for r in baseline_results.values() if r.get("is_blocked", False))
        self.logger.info(f"基准测试完成，{blocked_count}/{len(baseline_tests)} 个测试被风控拦截")

        return baseline_results

    async def _perform_baseline_test(self, endpoint: str, headers: Dict, test_name: str) -> Dict:
        """执行基准测试"""
        client = await self._get_client()

        # 基础请求头
        request_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
        }

        # 添加测试特定的头部
        request_headers.update(headers)

        start_time = time.time()

        try:
            response = await client.get(endpoint, headers=request_headers)
            response_time = time.time() - start_time

            # 分析响应
            is_blocked = self._analyze_risk_control_response(response)

            return {
                "test_name": test_name,
                "status_code": response.status_code,
                "response_time": response_time,
                "is_blocked": is_blocked,
                "response_headers": dict(response.headers),
                "response_text": response.text[:500] if response.text else "",
                "risk_indicators": self._extract_risk_indicators(response)
            }

        except Exception as e:
            return {
                "test_name": test_name,
                "error": str(e),
                "is_blocked": True,
                "response_time": time.time() - start_time
            }

    def _generate_invalid_fingerprint(self) -> Dict[str, str]:
        """生成无效的指纹用于基准测试"""
        return {
            "x-device-id": "INVALID-DEVICE-ID",
            "X-XHPAcPXq-z": "invalid",
            "X-XHPAcPXq-g": "invalid_base64_data",
            "X-XHPAcPXq-e": "invalid;data",
            "X-XHPAcPXq-f": "invalid",
            "X-XHPAcPXq-d": "invalid",
            "X-XHPAcPXq-c": "invalid",
            "X-XHPAcPXq-b": "invalid",
            "X-XHPAcPXq-a": "invalid",
            "Authorization": "invalid_auth",
            "x-bs-device-id": "invalid"
        }

    def _analyze_risk_control_response(self, response: httpx.Response) -> bool:
        """分析响应是否被风控拦截"""
        # 检查状态码
        if response.status_code in self.risk_control_patterns["blocked_status_codes"]:
            return True

        # 检查响应头
        for header_name, header_value in response.headers.items():
            header_lower = header_name.lower()
            for suspicious_header in self.risk_control_patterns["suspicious_headers"]:
                if suspicious_header in header_lower:
                    return True

        # 检查响应内容
        response_text = response.text.lower() if response.text else ""
        for keyword in self.risk_control_patterns["blocked_keywords"]:
            if keyword.lower() in response_text:
                return True

        # 检查挑战模式
        for pattern in self.risk_control_patterns["challenge_patterns"]:
            if pattern in response_text:
                return True

        # 检查JSON响应中的错误码
        try:
            if response.headers.get("content-type", "").startswith("application/json"):
                json_data = response.json()
                if isinstance(json_data, dict):
                    # 检查常见的错误字段
                    error_fields = ["error", "code", "status", "message"]
                    for field in error_fields:
                        if field in json_data:
                            error_value = str(json_data[field]).lower()
                            for keyword in self.risk_control_patterns["blocked_keywords"]:
                                if keyword.lower() in error_value:
                                    return True
        except:
            pass

        return False

    def _extract_risk_indicators(self, response: httpx.Response) -> List[str]:
        """提取风控指标"""
        indicators = []

        # 状态码指标
        if response.status_code in self.risk_control_patterns["blocked_status_codes"]:
            indicators.append(f"blocked_status_code_{response.status_code}")

        # 响应头指标
        for header_name, header_value in response.headers.items():
            if any(suspicious in header_name.lower() for suspicious in self.risk_control_patterns["suspicious_headers"]):
                indicators.append(f"suspicious_header_{header_name.lower()}")

        # 响应内容指标
        response_text = response.text.lower() if response.text else ""
        for keyword in self.risk_control_patterns["blocked_keywords"]:
            if keyword.lower() in response_text:
                indicators.append(f"blocked_keyword_{keyword}")

        return indicators

    def _verify_bypass_effectiveness(self, response: httpx.Response, is_blocked: bool) -> Dict[str, Any]:
        """验证绕过效果"""
        analysis = {
            "bypass_score": 0.0,
            "confidence": 0.0,
            "evidence": [],
            "comparison_with_baseline": {}
        }

        # 基础分数：如果没有被拦截，基础分数为0.5
        if not is_blocked:
            analysis["bypass_score"] += 0.5
            analysis["evidence"].append("未检测到明显的风控拦截")

        # 与基准对比
        if self.baseline_established:
            baseline_comparison = self._compare_with_baseline(response)
            analysis["comparison_with_baseline"] = baseline_comparison

            # 如果比基准测试表现更好，增加分数
            if baseline_comparison["better_than_no_fingerprint"]:
                analysis["bypass_score"] += 0.3
                analysis["evidence"].append("表现优于无指纹请求")

            if baseline_comparison["better_than_invalid_fingerprint"]:
                analysis["bypass_score"] += 0.2
                analysis["evidence"].append("表现优于无效指纹请求")

        # 检查正常业务响应特征
        business_response_score = self._analyze_business_response(response)
        analysis["bypass_score"] += business_response_score * 0.3

        if business_response_score > 0.7:
            analysis["evidence"].append("响应包含正常业务数据")

        # 计算置信度
        evidence_count = len(analysis["evidence"])
        analysis["confidence"] = min(evidence_count * 0.25, 1.0)

        # 限制分数范围
        analysis["bypass_score"] = max(0.0, min(1.0, analysis["bypass_score"]))

        return analysis

    def _compare_with_baseline(self, response: httpx.Response) -> Dict[str, bool]:
        """与基准测试对比"""
        comparison = {
            "better_than_no_fingerprint": False,
            "better_than_invalid_fingerprint": False,
            "similar_to_baseline": False
        }

        # 获取基准结果
        no_fp_result = self.baseline_results.get("no_fingerprint", {})
        invalid_fp_result = self.baseline_results.get("invalid_fingerprint", {})

        # 比较状态码
        current_status = response.status_code

        if no_fp_result.get("status_code"):
            no_fp_status = no_fp_result["status_code"]
            # 如果基准被拦截(4xx, 5xx)，而当前请求成功(2xx)，则表现更好
            if no_fp_status >= 400 and 200 <= current_status < 300:
                comparison["better_than_no_fingerprint"] = True

        if invalid_fp_result.get("status_code"):
            invalid_fp_status = invalid_fp_result["status_code"]
            if invalid_fp_status >= 400 and 200 <= current_status < 300:
                comparison["better_than_invalid_fingerprint"] = True

        # 比较是否被拦截
        current_blocked = self._analyze_risk_control_response(response)

        if no_fp_result.get("is_blocked", True) and not current_blocked:
            comparison["better_than_no_fingerprint"] = True

        if invalid_fp_result.get("is_blocked", True) and not current_blocked:
            comparison["better_than_invalid_fingerprint"] = True

        return comparison

    def _analyze_business_response(self, response: httpx.Response) -> float:
        """分析业务响应质量"""
        score = 0.0

        # 检查状态码
        if 200 <= response.status_code < 300:
            score += 0.4
        elif response.status_code == 304:  # Not Modified
            score += 0.3

        # 检查响应头
        content_type = response.headers.get("content-type", "").lower()
        if "application/json" in content_type:
            score += 0.2
        elif "text/html" in content_type:
            score += 0.1

        # 检查响应内容
        try:
            if response.text:
                response_text = response.text.lower()

                # 正面指标
                positive_indicators = ["data", "result", "success", "list", "info"]
                for indicator in positive_indicators:
                    if indicator in response_text:
                        score += 0.1
                        break

                # 检查JSON结构
                if content_type.startswith("application/json"):
                    try:
                        json_data = response.json()
                        if isinstance(json_data, dict):
                            # 有数据字段
                            if any(key in json_data for key in ["data", "result", "list", "items"]):
                                score += 0.2
                            # 有成功状态
                            if json_data.get("code") == 0 or json_data.get("status") == "success":
                                score += 0.1
                    except:
                        pass
        except:
            pass

        return min(score, 1.0)

    async def _get_client(self) -> httpx.AsyncClient:
        """获取HTTP客户端"""
        if self.client is None:
            self.client = httpx.AsyncClient(
                timeout=settings.REQUEST_TIMEOUT,
                limits=httpx.Limits(max_connections=settings.MAX_DEVICES * 2)
            )
        return self.client
    
    async def test_single_device(self, device: Device, test_endpoint: str) -> Dict:
        """
        测试单个设备的风控绕过效果 - 增强版

        Args:
            device: 设备对象
            test_endpoint: 测试端点

        Returns:
            测试结果字典
        """
        # 确保基准已建立
        if not self.baseline_established:
            await self.establish_baseline(test_endpoint)

        start_time = time.time()
        result = {
            "device_index": device.device_index,
            "device_id": device.device_id,
            "endpoint": test_endpoint,
            "success": False,
            "bypass_confirmed": False,
            "response_time": 0,
            "status_code": 0,
            "error_message": "",
            "risk_indicators": [],
            "bypass_analysis": {},
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        try:
            client = await self._get_client()

            # 构建请求头
            headers = self._build_headers(device.fingerprint)

            # 发送测试请求
            response = await client.get(test_endpoint, headers=headers)

            result["status_code"] = response.status_code
            result["response_time"] = time.time() - start_time

            # 分析风控响应
            is_blocked = self._analyze_risk_control_response(response)
            risk_indicators = self._extract_risk_indicators(response)

            result["risk_indicators"] = risk_indicators

            # 真实绕过验证
            bypass_analysis = self._verify_bypass_effectiveness(response, is_blocked)
            result["bypass_analysis"] = bypass_analysis

            # 判断绕过效果
            if not is_blocked and bypass_analysis["bypass_score"] >= 0.8:
                result["success"] = True
                result["bypass_confirmed"] = True
                result["error_message"] = f"成功绕过风控，绕过评分: {bypass_analysis['bypass_score']:.2f}"

                # 更新设备状态为成功
                device.last_success_time = datetime.now()
                device.success_count += 1

            elif not is_blocked and bypass_analysis["bypass_score"] >= 0.5:
                result["success"] = True
                result["bypass_confirmed"] = False
                result["error_message"] = f"可能绕过风控，绕过评分: {bypass_analysis['bypass_score']:.2f}"

            else:
                result["success"] = False
                result["bypass_confirmed"] = False
                result["error_message"] = f"风控拦截，绕过评分: {bypass_analysis['bypass_score']:.2f}"

                # 更新设备状态为失败
                device.failure_count += 1

            # 记录测试日志
            log_bypass_test(
                device_index=device.device_index,
                success=result["success"],
                response_time=result["response_time"],
                status_code=result["status_code"],
                bypass_score=bypass_analysis.get("bypass_score", 0.0)
            )
            
        except httpx.TimeoutException:
            result["error_message"] = "请求超时"
            result["response_time"] = time.time() - start_time
            
        except httpx.ConnectError:
            result["error_message"] = "连接失败"
            result["response_time"] = time.time() - start_time
            
        except Exception as e:
            result["error_message"] = f"测试异常: {str(e)}"
            result["response_time"] = time.time() - start_time
            log_error_with_context(e, {"device_index": device.device_index, "endpoint": test_endpoint})
        
        # 记录测试日志
        log_bypass_test(
            device.device_index,
            test_endpoint,
            result["success"],
            result["response_time"],
            result["error_message"]
        )
        
        return result
    
    def _build_headers(self, fingerprint: Dict[str, str]) -> Dict[str, str]:
        """
        构建请求头
        
        Args:
            fingerprint: 设备指纹
            
        Returns:
            请求头字典
        """
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache"
        }
        
        # 添加F5 Shape指纹头
        f5_headers = [
            "x-device-id", "X-XHPAcPXq-z", "X-XHPAcPXq-g", "X-XHPAcPXq-e",
            "X-XHPAcPXq-f", "X-XHPAcPXq-d", "X-XHPAcPXq-c", "X-XHPAcPXq-b",
            "X-XHPAcPXq-a", "Authorization", "x-bs-device-id"
        ]
        
        for header in f5_headers:
            if header in fingerprint and fingerprint[header]:
                headers[header] = fingerprint[header]
        
        return headers
    
    def _is_blocked_response(self, response_data: Dict, response_text: str) -> bool:
        """
        判断响应是否被风控拦截
        
        Args:
            response_data: 响应JSON数据
            response_text: 响应文本
            
        Returns:
            是否被拦截
        """
        # 检查响应数据中的错误码
        if isinstance(response_data, dict):
            error_code = response_data.get("code", response_data.get("error_code", 0))
            # 星巴克API常见的风控错误码
            if error_code in [403, 429, 1001, 1002, 1003, 4001, 4002, 4003]:
                return True

            # 检查success字段
            if "success" in response_data and not response_data["success"]:
                message = response_data.get("message", response_data.get("msg", "")).lower()
                for indicator in settings.BLOCKED_INDICATORS:
                    if indicator.lower() in message:
                        return True

            # 检查响应数据中的字符串值
            for key, value in response_data.items():
                if isinstance(value, str):
                    for indicator in settings.BLOCKED_INDICATORS:
                        if indicator.lower() in value.lower():
                            return True

        # 检查响应文本
        response_text_lower = response_text.lower()
        for indicator in settings.BLOCKED_INDICATORS:
            if indicator.lower() in response_text_lower:
                return True

        return False

    def _is_successful_response(self, response_data: Dict, status_code: int) -> bool:
        """
        判断响应是否成功（未被风控拦截）

        Args:
            response_data: 响应JSON数据
            status_code: HTTP状态码

        Returns:
            是否成功绕过风控
        """
        # HTTP状态码检查
        if status_code != 200:
            return False

        # 检查响应数据
        if isinstance(response_data, dict):
            # 星巴克API成功响应特征
            if "data" in response_data or "result" in response_data:
                return True

            # 检查success字段
            if "success" in response_data:
                return response_data["success"] is True

            # 检查code字段
            code = response_data.get("code", response_data.get("error_code"))
            if code == 0 or code == 200:
                return True

        return True  # 默认认为成功
    
    async def test_concurrent_devices(self, device_count: int, test_endpoint: str) -> List[Dict]:
        """
        并发测试多个设备
        
        Args:
            device_count: 设备数量
            test_endpoint: 测试端点
            
        Returns:
            测试结果列表
        """
        if device_count <= 0:
            raise ValueError("设备数量必须大于0")
        
        if device_count > settings.MAX_DEVICES:
            device_count = settings.MAX_DEVICES
            self.logger.warning(f"设备数量超过最大限制，调整为 {settings.MAX_DEVICES}")
        
        # 获取可用设备
        available_devices = []
        for i in range(device_count):
            device = device_manager.get_available_device()
            if device:
                available_devices.append(device)
            else:
                self.logger.warning(f"无法获取第 {i+1} 个可用设备")
                break
        
        if not available_devices:
            raise Exception("没有可用的设备进行测试")
        
        self.logger.info(f"开始并发测试 {len(available_devices)} 个设备")
        
        # 创建并发任务
        tasks = []
        for device in available_devices:
            task = asyncio.create_task(
                self.test_single_device(device, test_endpoint)
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        test_results = []
        for i, result in enumerate(results):
            device = available_devices[i]
            
            if isinstance(result, Exception):
                # 任务异常
                test_result = {
                    "device_index": device.device_index,
                    "device_id": device.device_id,
                    "endpoint": test_endpoint,
                    "success": False,
                    "response_time": 0,
                    "status_code": 0,
                    "error_message": f"任务异常: {str(result)}",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                log_error_with_context(result, {"device_index": device.device_index})
            else:
                test_result = result
            
            test_results.append(test_result)
            
            # 释放设备
            device_manager.release_device(device.device_index, test_result["success"])
        
        # 统计结果
        success_count = sum(1 for result in test_results if result["success"])
        success_rate = success_count / len(test_results) if test_results else 0
        
        self.logger.info(f"并发测试完成，成功率: {success_rate:.2%} ({success_count}/{len(test_results)})")
        
        return test_results
    
    async def update_device_status_from_results(self, test_results: List[Dict]):
        """
        根据测试结果更新设备状态
        
        Args:
            test_results: 测试结果列表
        """
        for result in test_results:
            device_index = result["device_index"]
            success = result["success"]
            
            # 更新设备统计
            device_manager.release_device(device_index, success)
            
            # 如果连续失败，考虑封禁设备
            if not success:
                device = device_manager.devices.get(device_index)
                if device and device.failure_count >= 5 and device.success_rate < 0.3:
                    device_manager.block_device(device_index, "连续失败次数过多")
    
    async def stress_test(self, duration_minutes: int = 10, concurrent_devices: int = 10) -> Dict:
        """
        压力测试
        
        Args:
            duration_minutes: 测试持续时间（分钟）
            concurrent_devices: 并发设备数
            
        Returns:
            压力测试结果
        """
        self.logger.info(f"开始压力测试，持续时间: {duration_minutes}分钟，并发设备: {concurrent_devices}")
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        total_requests = 0
        total_success = 0
        total_errors = 0
        response_times = []
        
        while time.time() < end_time:
            try:
                results = await self.test_concurrent_devices(concurrent_devices, settings.TEST_ENDPOINT)
                
                total_requests += len(results)
                for result in results:
                    if result["success"]:
                        total_success += 1
                    else:
                        total_errors += 1
                    response_times.append(result["response_time"])
                
                # 短暂休息
                await asyncio.sleep(1)
                
            except Exception as e:
                total_errors += 1
                log_error_with_context(e, {"test_type": "stress_test"})
        
        # 计算统计数据
        actual_duration = time.time() - start_time
        success_rate = total_success / total_requests if total_requests > 0 else 0
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        requests_per_second = total_requests / actual_duration if actual_duration > 0 else 0
        
        stress_result = {
            "duration_seconds": actual_duration,
            "total_requests": total_requests,
            "total_success": total_success,
            "total_errors": total_errors,
            "success_rate": success_rate,
            "avg_response_time": avg_response_time,
            "requests_per_second": requests_per_second,
            "concurrent_devices": concurrent_devices
        }
        
        self.logger.info(f"压力测试完成: {stress_result}")
        
        return stress_result
    
    async def close(self):
        """关闭测试器"""
        if self.client:
            await self.client.aclose()
            self.client = None


# 全局测试器实例
bypass_tester = BypassTester()
