#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI主应用
作者：YINGAshadow
创建时间：2025-7-29
功能：提供HTTP API接口
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from ..config.settings import settings
from ..core.device_manager import device_manager, DeviceStatus
from ..core.f5_shape_generator import f5_generator
from ..utils.logger import setup_logger
from ..utils.bypass_tester import bypass_tester
from ..utils.auth import get_current_user, get_admin_user, check_rate_limit, get_client_ip, verify_api_key_header
from ..utils.monitor import system_monitor


# 设置日志
logger = setup_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title=settings.API_TITLE,
    description=settings.API_DESCRIPTION,
    version=settings.API_VERSION,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 请求模型
class FingerprintRequest(BaseModel):
    """指纹生成请求"""
    device_count: int = 1
    force_regenerate: bool = False


class TestRequest(BaseModel):
    """风控测试请求"""
    device_index: Optional[int] = None
    test_endpoint: Optional[str] = None
    concurrent_count: int = 1


class DeviceOperationRequest(BaseModel):
    """设备操作请求"""
    device_index: int
    operation: str  # "block", "regenerate", "release"
    reason: Optional[str] = ""


class CustomerTestRequest(BaseModel):
    """客户测试服务请求模型"""
    target_url: str
    test_config: Dict = {
        "device_count": 30,
        "method": "GET",
        "headers": {},
        "data": {},
        "concurrent_limit": 10,
        "delay_between_requests": 0.1
    }


# 响应模型
class ApiResponse(BaseModel):
    """API响应基类"""
    success: bool
    message: str
    data: Optional[Dict] = None
    timestamp: str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")


class FingerprintResponse(ApiResponse):
    """指纹响应"""
    fingerprints: Optional[List[Dict]] = None


class TestResponse(ApiResponse):
    """测试响应"""
    test_results: Optional[List[Dict]] = None


# 全局变量
concurrent_requests = 0
max_concurrent = settings.MAX_CONCURRENT_REQUESTS


@app.middleware("http")
async def request_middleware(request: Request, call_next):
    """请求中间件：限流、监控、日志"""
    global concurrent_requests

    start_time = time.time()
    client_ip = get_client_ip(request)

    # 检查并发限制
    if concurrent_requests >= max_concurrent:
        return JSONResponse(
            status_code=429,
            content={"success": False, "message": "服务器繁忙，请稍后重试"}
        )

    # 检查频率限制（对于非认证端点）
    if not request.url.path.startswith("/docs") and not request.url.path.startswith("/redoc"):
        try:
            await check_rate_limit(request)
        except HTTPException as e:
            return JSONResponse(
                status_code=e.status_code,
                content={"success": False, "message": e.detail},
                headers=e.headers
            )

    # 读取请求体（用于监控）
    request_body = ""
    if request.method in ["POST", "PUT", "PATCH"]:
        try:
            body = await request.body()
            request_body = body.decode('utf-8') if body else ""
        except:
            request_body = ""

    concurrent_requests += 1
    try:
        response = await call_next(request)

        # 记录API指标
        response_time = time.time() - start_time
        success = response.status_code < 400
        system_monitor.metrics_collector.record_api_request(success, response_time)

        # 获取客户ID和用户信息
        customer_id = "system"
        user_type = "internal"

        # 检查是否是客户API
        if "/api/bypass/" in str(request.url.path):
            user_type = "customer"
            try:
                # 从X-API-Key头获取客户API密钥
                api_key = request.headers.get("X-API-Key", "")
                if api_key:
                    customer_id = f"customer_{api_key[:8]}"
                else:
                    customer_id = "unknown_customer"
            except:
                customer_id = "unknown_customer"
        else:
            # 检查是否有认证信息
            try:
                auth_header = request.headers.get("Authorization", "")
                if auth_header.startswith("Bearer "):
                    token = auth_header[7:]
                    customer_id = f"admin_{token[:8]}"
                    user_type = "admin"
                else:
                    customer_id = f"anonymous_{client_ip.replace('.', '_')}"
                    user_type = "anonymous"
            except:
                customer_id = f"anonymous_{client_ip.replace('.', '_')}"
                user_type = "anonymous"

        # 读取响应体（用于监控）
        response_body = ""
        try:
            if hasattr(response, 'body'):
                response_body = str(response.body)[:1000]  # 限制长度
        except:
            response_body = ""

        # 记录到监控后台（所有API请求）
        # 排除健康检查和静态资源
        if not any(path in str(request.url.path) for path in ["/health", "/favicon.ico", "/static/"]):
            try:
                await system_monitor.metrics_collector.record_all_requests(
                    client_ip=client_ip,
                    customer_id=customer_id,
                    user_type=user_type,
                    endpoint=str(request.url.path),
                    method=request.method,
                    headers=dict(request.headers),
                    request_body=request_body[:1000],  # 限制长度
                    response_status=response.status_code,
                    response_body=response_body,
                    response_time=response_time,
                    user_agent=request.headers.get("User-Agent", "")
                )
            except Exception as e:
                logger.error(f"发送监控日志失败: {str(e)}")

        # 记录访问日志
        logger.info(f"API请求 - IP: {client_ip}, 路径: {request.url.path}, "
                   f"方法: {request.method}, 状态: {response.status_code}, "
                   f"响应时间: {response_time:.3f}s, 客户: {customer_id}")

        return response
    finally:
        concurrent_requests -= 1


@app.get("/", response_model=ApiResponse)
async def root():
    """根路径"""
    return ApiResponse(
        success=True,
        message="星巴克设备指纹风控绕过系统运行正常",
        data={
            "version": settings.API_VERSION,
            "max_devices": settings.MAX_DEVICES,
            "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    )


@app.get("/health", response_model=ApiResponse)
async def health_check():
    """健康检查"""
    try:
        stats = device_manager.get_device_stats()
        return ApiResponse(
            success=True,
            message="系统健康状态良好",
            data=stats
        )
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@app.post("/api/v1/fingerprint/generate", response_model=FingerprintResponse)
async def generate_fingerprint(request: FingerprintRequest):
    """生成设备指纹"""
    try:
        if request.device_count <= 0 or request.device_count > settings.MAX_DEVICES:
            raise HTTPException(
                status_code=400, 
                detail=f"设备数量必须在1-{settings.MAX_DEVICES}之间"
            )
        
        fingerprints = []
        
        if request.force_regenerate:
            # 强制重新生成
            for i in range(request.device_count):
                fingerprint = f5_generator.generate_fingerprint(i)
                fingerprints.append(fingerprint)
                logger.info(f"强制重新生成设备 {i} 的指纹")
        else:
            # 使用现有设备
            for i in range(request.device_count):
                device = device_manager.devices.get(i)
                if device:
                    fingerprints.append(device.fingerprint)
                else:
                    # 如果设备不存在，生成新的
                    fingerprint = f5_generator.generate_fingerprint(i)
                    fingerprints.append(fingerprint)
        
        logger.info(f"成功生成 {len(fingerprints)} 个设备指纹")
        
        return FingerprintResponse(
            success=True,
            message=f"成功生成 {len(fingerprints)} 个设备指纹",
            fingerprints=fingerprints
        )
        
    except Exception as e:
        logger.error(f"生成指纹失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成指纹失败: {str(e)}")


@app.get("/api/v1/fingerprint/{device_index}", response_model=FingerprintResponse)
async def get_device_fingerprint(device_index: int):
    """获取指定设备的指纹"""
    try:
        if device_index < 0 or device_index >= settings.MAX_DEVICES:
            raise HTTPException(
                status_code=400,
                detail=f"设备索引必须在0-{settings.MAX_DEVICES-1}之间"
            )
        
        device = device_manager.devices.get(device_index)
        if not device:
            raise HTTPException(status_code=404, detail=f"设备 {device_index} 不存在")
        
        return FingerprintResponse(
            success=True,
            message=f"获取设备 {device_index} 指纹成功",
            fingerprints=[device.fingerprint]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取设备指纹失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取设备指纹失败: {str(e)}")


@app.post("/api/v1/test/bypass", response_model=TestResponse)
async def test_bypass(request: TestRequest, background_tasks: BackgroundTasks):
    """测试风控绕过效果"""
    try:
        # 使用真实的星巴克API端点
        test_endpoint = request.test_endpoint or settings.DEFAULT_TEST_ENDPOINT
        
        if request.device_index is not None:
            # 测试指定设备
            if request.device_index < 0 or request.device_index >= settings.MAX_DEVICES:
                raise HTTPException(
                    status_code=400,
                    detail=f"设备索引必须在0-{settings.MAX_DEVICES-1}之间"
                )
            
            device = device_manager.devices.get(request.device_index)
            if not device:
                raise HTTPException(status_code=404, detail=f"设备 {request.device_index} 不存在")
            
            result = await bypass_tester.test_single_device(device, test_endpoint, "GET")
            test_results = [result]
            
        else:
            # 并发测试多个设备
            concurrent_count = min(request.concurrent_count, settings.MAX_DEVICES)
            test_results = await bypass_tester.test_concurrent_devices(
                concurrent_count, test_endpoint
            )
        
        # 后台任务：更新设备状态
        background_tasks.add_task(
            bypass_tester.update_device_status_from_results, test_results
        )
        
        success_count = sum(1 for result in test_results if result.get("success", False))
        success_rate = success_count / len(test_results) if test_results else 0
        
        logger.info(f"风控测试完成，成功率: {success_rate:.2%}")
        
        return TestResponse(
            success=True,
            message=f"测试完成，成功率: {success_rate:.2%}",
            test_results=test_results,
            data={
                "total_tests": len(test_results),
                "success_count": success_count,
                "success_rate": success_rate
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"风控测试失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"风控测试失败: {str(e)}")


@app.get("/api/v1/devices", response_model=ApiResponse)
async def get_devices():
    """获取设备列表"""
    try:
        device_list = device_manager.get_device_list()
        stats = device_manager.get_device_stats()
        
        return ApiResponse(
            success=True,
            message="获取设备列表成功",
            data={
                "devices": device_list,
                "statistics": stats
            }
        )
        
    except Exception as e:
        logger.error(f"获取设备列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取设备列表失败: {str(e)}")


@app.post("/api/v1/devices/operation", response_model=ApiResponse)
async def device_operation(request: DeviceOperationRequest):
    """设备操作"""
    try:
        device_index = request.device_index
        operation = request.operation.lower()
        
        if device_index < 0 or device_index >= settings.MAX_DEVICES:
            raise HTTPException(
                status_code=400,
                detail=f"设备索引必须在0-{settings.MAX_DEVICES-1}之间"
            )
        
        if operation == "block":
            device_manager.block_device(device_index, request.reason)
            message = f"设备 {device_index} 已被封禁"
            
        elif operation == "regenerate":
            success = device_manager.regenerate_device(device_index)
            if not success:
                raise HTTPException(status_code=500, detail=f"重新生成设备 {device_index} 失败")
            message = f"设备 {device_index} 指纹已重新生成"
            
        elif operation == "release":
            device_manager.release_device(device_index, True)
            message = f"设备 {device_index} 已释放"
            
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {operation}")
        
        logger.info(f"设备操作成功: {message}")
        
        return ApiResponse(
            success=True,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设备操作失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"设备操作失败: {str(e)}")


@app.post("/api/v1/devices/cleanup", response_model=ApiResponse)
async def cleanup_devices(current_user: str = Depends(get_admin_user)):
    """清理被封设备（需要管理员权限）"""
    try:
        device_manager.cleanup_blocked_devices()

        logger.info(f"管理员 {current_user} 执行清理被封设备操作")

        return ApiResponse(
            success=True,
            message="被封设备清理完成",
            data={"operator": current_user}
        )

    except Exception as e:
        logger.error(f"清理设备失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理设备失败: {str(e)}")


@app.get("/api/v1/system/status", response_model=ApiResponse)
async def get_system_status(current_user: str = Depends(get_current_user)):
    """获取系统状态（需要认证）"""
    try:
        # 获取监控状态
        monitor_status = system_monitor.get_status()

        # 获取设备统计
        device_stats = device_manager.get_device_stats()

        # 获取活跃告警
        active_alerts = system_monitor.alert_manager.get_active_alerts()

        return ApiResponse(
            success=True,
            message="获取系统状态成功",
            data={
                "monitor_status": monitor_status,
                "device_stats": device_stats,
                "active_alerts": len(active_alerts),
                "alerts": [
                    {
                        "id": alert.alert_id,
                        "type": alert.alert_type,
                        "level": alert.level,
                        "message": alert.message,
                        "timestamp": alert.timestamp
                    } for alert in active_alerts[:10]  # 只显示前10个告警
                ]
            }
        )

    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


# 客户服务接口
@app.post("/api/bypass/test-service", response_model=ApiResponse)
async def customer_bypass_test_service(
    request: CustomerTestRequest,
    customer_id: str = Depends(verify_api_key_header)
):
    """
    客户风控绕过测试服务
    为客户提供的风控绕过能力测试接口
    """
    try:
        logger.info(f"客户 {customer_id} 请求测试: {request.target_url}")

        # 获取测试配置
        config = request.test_config
        device_count = config.get("device_count", 30)
        method = config.get("method", "GET")
        headers = config.get("headers", {})
        data = config.get("data", {})
        concurrent_limit = config.get("concurrent_limit", 10)
        delay = config.get("delay_between_requests", 0.1)

        # 执行并发绕过测试
        test_results = []
        successful_bypasses = 0
        total_response_time = 0

        # 分批并发测试
        for batch_start in range(0, device_count, concurrent_limit):
            batch_end = min(batch_start + concurrent_limit, device_count)
            batch_tasks = []

            for device_index in range(batch_start, batch_end):
                # 获取设备对象
                device = device_manager.devices.get(device_index)
                if not device:
                    # 如果设备不存在，跳过
                    test_results.append({
                        "device_index": device_index,
                        "success": False,
                        "error": f"设备 {device_index} 不存在"
                    })
                    continue

                # 使用正确的方法签名调用，传递HTTP方法和数据
                task = bypass_tester.test_single_device(
                    device=device,
                    test_endpoint=request.target_url,
                    method=method,
                    headers=headers,
                    data=data
                )
                batch_tasks.append(task)

            # 执行当前批次
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            for i, result in enumerate(batch_results):
                device_index = batch_start + i
                if isinstance(result, Exception):
                    test_results.append({
                        "device_index": device_index,
                        "success": False,
                        "error": str(result)
                    })
                else:
                    test_results.append(result)
                    # 使用正确的字段名
                    if result.get("success", False) or result.get("bypass_confirmed", False):
                        successful_bypasses += 1
                    total_response_time += result.get("response_time", 0)

            # 批次间延迟
            if batch_end < device_count:
                await asyncio.sleep(delay)

        # 计算统计数据
        success_rate = successful_bypasses / device_count if device_count > 0 else 0
        avg_response_time = total_response_time / device_count if device_count > 0 else 0
        blocked_requests = device_count - successful_bypasses

        # 计算绕过评分
        bypass_score = min(success_rate + 0.1, 1.0)  # 基础评分加成

        # 效果评估
        if bypass_score >= 0.8:
            effectiveness = "优秀"
            recommendation = "该接口风控可以被有效绕过，建议使用我们的服务"
        elif bypass_score >= 0.6:
            effectiveness = "良好"
            recommendation = "该接口风控可以被部分绕过，有一定效果"
        else:
            effectiveness = "一般"
            recommendation = "该接口风控较强，绕过效果有限"

        return ApiResponse(
            success=True,
            message="风控绕过测试完成",
            data={
                "service_name": "星巴克F5 Shape风控绕过测试服务",
                "test_result": {
                    "target_url": request.target_url,
                    "bypass_success_rate": round(success_rate, 3),
                    "bypass_score": round(bypass_score, 2),
                    "total_tests": device_count,
                    "successful_bypasses": successful_bypasses,
                    "blocked_requests": blocked_requests,
                    "average_response_time": round(avg_response_time, 3),
                    "effectiveness": effectiveness,
                    "recommendation": recommendation
                },
                "service_info": {
                    "technology": "F5 Shape指纹技术",
                    "sample_base": "435个真实样本",
                    "concurrent_devices": device_count,
                    "test_method": method,
                    "test_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                },
                "technical_details": {
                    "fingerprint_technology": "F5 Shape设备指纹",
                    "bypass_algorithm": "多层编码绕过算法",
                    "device_simulation": "真实设备特征模拟",
                    "success_criteria": "HTTP状态码200且无风控拦截标识"
                }
            }
        )

    except Exception as e:
        logger.error(f"客户测试服务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试服务失败: {str(e)}")


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("星巴克设备指纹风控绕过系统启动中...")

    # 执行设备健康检查
    await device_manager.health_check()

    # 启动系统监控
    await system_monitor.start_monitoring(interval=60)

    logger.info(f"系统启动完成，监听端口: {settings.PORT}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("星巴克设备指纹风控绕过系统正在关闭...")

    # 停止系统监控
    await system_monitor.stop_monitoring()


if __name__ == "__main__":
    uvicorn.run(
        "src.api.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
